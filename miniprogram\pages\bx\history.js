const app = getApp();
Page({
  data: {
    list: [],
    loading: true
  },
  onLoad() {
    this.getHistory();
  },
  async getHistory() {
    this.setData({ loading: true });
    try {
      const res = await app.request({
        url: '/api/wx/bx/history',
        method: 'POST'
      });
      if (res.code === 0) {
        const list = (res.data || []).map(item => {
          let reply_content = '';
          if (item.status == 2) {
            reply_content = item.reply_content || '';
          }

          let imageUrls = [];
          try {
            if (item.media_urls) {
              const parsed = JSON.parse(item.media_urls);
              // 直接提取图片URL
              imageUrls = parsed.map(media => media.url || media);
            }
          } catch (e) {
            console.warn('解析media_urls失败:', e);
            imageUrls = [];
          }

          return {
            ...item,
            imageUrls,
            statusText: this.getStatusText(item.status),
            statusColor: this.getStatusColor(item.status),
            reply_content
          };
        });
        this.setData({ list, loading: false });
      } else {
        this.setData({ list: [], loading: false });
      }
    } catch (e) {
      this.setData({ list: [], loading: false });
    }
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      '0': '未处理',
      '1': '处理中',
      '2': '已完成'
    };
    return statusMap[status] || '未处理';
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colorMap = {
      '0': '#ff4d4f',  // 红色
      '1': '#faad14',  // 橙色
      '2': '#52c41a'   // 绿色
    };
    return colorMap[status] || '#ff4d4f';
  },



  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    const { item } = e.currentTarget.dataset;

    // 直接使用图片URL数组进行预览
    if (item.imageUrls && item.imageUrls.length > 0) {
      wx.previewImage({
        current: url,
        urls: item.imageUrls
      });
    }
  },

  // 跳转到详情页面
  goToDetail(e) {
    const { id } = e.currentTarget.dataset;
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/bx/detail?id=${id}`
    });
  },

  onPullDownRefresh() {
    this.getHistory().then(() => wx.stopPullDownRefresh());
  }
});