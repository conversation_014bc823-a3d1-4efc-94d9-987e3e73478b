// 通用反馈管理器
import { FEEDBACK_TYPES, REPAIR_OPTIONS, COMPLAINT_OPTIONS } from '../constants/index.js'

class FeedbackManager {
  constructor(type) {
    this.type = type // 'bx' 或 'complaint'
    this.apiPrefix = `/api/wx/${type}`
    this.typeOptions = type === FEEDBACK_TYPES.REPAIR ? REPAIR_OPTIONS : COMPLAINT_OPTIONS
  }

  // 获取选项列表
  getTypeOptions() {
    return this.typeOptions
  }

  // 验证表单数据
  validateForm(formData) {
    const { type, content, address } = formData

    if (!type) {
      return { valid: false, message: '请选择类型' }
    }

    if (!content || content.trim().length < 10) {
      return { valid: false, message: '请输入至少10个字符的反馈内容' }
    }

    if (!address) {
      return { valid: false, message: '请输入详细地址' }
    }

    return { valid: true }
  }

  // 提交反馈
  async submitFeedback(formData) {
    try {
      const app = getApp()
      const result = await app.request({
        url: `${this.apiPrefix}/addData`,
        method: 'POST',
        data: {
          ...formData,
          media_urls: JSON.stringify(formData.mediaUrls || [])
        }
      })
      if (result.code === 0) {
        return { success: true, data: result.data }
      } else {
        throw new Error(result.msg || '提交失败')
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  // 生成业务ID
  generateBusinessId() {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11)
  }



  // 上传媒体文件
  async uploadMedia(filePath, businessId = null) {
    return new Promise((resolve, reject) => {
      const app = getApp()
      // 使用统一的文件上传接口
      const fullUrl = `${app.globalData.baseUrl}/api/wx/file/upload`
      const token = wx.getStorageSync('token')
      const headers = token ? { 'Authorization': `Bearer ${token}` } : {}

      const formData = {
        source: this.type, // 传递source参数区分文件来源
        bucketType: 'public' // 使用公共Bucket，确保图片永久可访问
      }

      // 如果有业务ID，则传递
      if (businessId) {
        formData.businessId = businessId
      }

      wx.uploadFile({
        url: fullUrl,
        filePath: filePath,
        name: 'file',
        formData: formData,
        header: headers,
        success: (uploadRes) => {
          try {
            const data = JSON.parse(uploadRes.data)
            if (data.code === 0) {
              let fileUrl = data.url
              if (fileUrl && !fileUrl.startsWith('http')) {
                fileUrl = app.globalData.baseUrl + fileUrl
              }
              resolve({
                url: fileUrl,
                fileId: data.fileId // 返回文件ID
              })
            } else {
              reject(new Error(data.msg || '上传失败'))
            }
          } catch (parseError) {
            reject(new Error('上传响应解析失败'))
          }
        },
        fail: (error) => {
          reject(new Error(error.errMsg || '上传失败'))
        }
      })
    })
  }

  // 检查文件是否为图片
  isImageFile(file) {
    // 检查文件类型字段
    if (file.type === 'image') {
      return true
    }

    // 检查文件扩展名
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp']
    const fileName = file.name || file.path || file.tempFilePath || file.url || ''
    const fileExtension = fileName.split('.').pop()?.toLowerCase()
    return imageTypes.includes(fileExtension)
  }

  // 压缩图片
  async compressImage(filePath) {
    try {
      const result = await new Promise((resolve, reject) => {
        wx.compressImage({
          src: filePath,
          quality: 80, // 压缩质量，范围0-100
          success: resolve,
          fail: reject
        })
      })
      return result.tempFilePath
    } catch (error) {
      console.warn('图片压缩失败，使用原图:', error)
      return filePath
    }
  }

  // 通用文件上传处理
  async handleFileUpload(file, currentFiles, loadingManager, handleError, businessId = null) {
    try {
      loadingManager.show('上传中...')

      const files = Array.isArray(file) ? file : [file]

      // 检查文件类型，只允许图片
      for (const singleFile of files) {
        if (!this.isImageFile(singleFile)) {
          throw new Error('只能上传图片文件（jpg、png、gif、webp格式）')
        }
      }

      const uploadResults = await Promise.all(
        files.map(async (singleFile) => {
          let filePath = singleFile.url || singleFile.tempFilePath || singleFile.path
          if (!filePath) {
            throw new Error('文件路径获取失败')
          }

          // 压缩图片
          filePath = await this.compressImage(filePath)

          const uploadResult = await this.uploadMedia(filePath, businessId)
          return {
            url: uploadResult.url,
            fileId: uploadResult.fileId, // 保存文件ID
            thumb: uploadResult.url,
            name: singleFile.name || '图片',
            size: singleFile.size || 0,
            isImage: true, // 明确标识为图片，确保van-uploader正确显示预览
            deletable: true // 允许删除
          }
        })
      )

      const newFiles = [...currentFiles, ...uploadResults]

      wx.showToast({
        title: `上传成功 ${uploadResults.length} 张图片`,
        icon: 'success',
        duration: 1500
      })

      return newFiles
    } catch (error) {
      handleError(error, '图片上传')
      return currentFiles
    } finally {
      loadingManager.hide()
    }
  }

  // 删除后端文件
  async deleteServerFile(fileId) {
    try {
      const app = getApp()
      const result = await app.request({
        url: '/file/delete',
        method: 'POST',
        data: {
          fileIds: fileId
        }
      })
      return result.code === 0
    } catch (error) {
      console.warn('删除服务器文件失败:', error)
      return false
    }
  }

  // 通用文件删除处理
  async handleFileDelete(index, currentFiles) {
    const updatedFiles = [...currentFiles]
    const deletedFile = updatedFiles[index]

    // 如果文件有fileId，尝试删除服务器文件
    if (deletedFile && deletedFile.fileId) {
      await this.deleteServerFile(deletedFile.fileId)
    }

    updatedFiles.splice(index, 1)

    wx.showToast({
      title: '删除成功',
      icon: 'success',
      duration: 1000
    })

    return updatedFiles
  }

  // 预填充用户信息
  getPrefilledForm() {
    try {
      const ownerInfo = wx.getStorageSync('ownerInfo') || {}
      return {
        type: '',
        content: '',
        mediaUrls: [],
        address: ownerInfo.houseStr || '',
        name: ownerInfo.ownerName || '',
        phone: ownerInfo.mobile || ''
      }
    } catch (error) {
      return {
        type: '',
        content: '',
        mediaUrls: [],
        address: '',
        name: '',
        phone: ''
      }
    }
  }
}

// 工厂函数
export function createFeedbackManager(type) {
  return new FeedbackManager(type)
}

export default FeedbackManager 