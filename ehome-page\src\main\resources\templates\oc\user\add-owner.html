<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('新增业主')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-owner-add">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">姓名：</label>
                <div class="col-sm-4">
                    <input id="owner_name" name="owner_name" class="form-control" type="text" required>
                </div>
                <label class="col-sm-2 control-label is-required">手机号：</label>
                <div class="col-sm-4">
                    <input id="mobile" name="mobile" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">性别：</label>
                <div class="col-sm-4">
                    <select name="gender" class="form-control">
                        <option value="M">男</option>
                        <option value="F">女</option>
                    </select>
                </div>
                <label class="col-sm-2 control-label">身份证号码：</label>
                <div class="col-sm-4">
                    <input id="id_card" name="id_card" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">住户状态：</label>
                <div class="col-sm-4">
                    <select name="is_live" class="form-control">
                        <option value="0">未入住</option>
                        <option value="1" >已入住</option>
                        <option value="2" >已迁出</option>
                    </select>
                </div>
                <label class="col-sm-2 control-label">入住日期：</label>
                <div class="col-sm-4">
                    <input name="move_date" type="text" class="form-control time-input">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea id="remark" name="remark" style="height: 70px;" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/owner";
        
        $("#form-owner-add").validate({
            focusCleanup: true
        });
        
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-owner-add').serialize());
            }
        }
    </script>
</body>
</html>
