package com.ehome.oc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.exception.ServiceException;
import com.ehome.common.utils.IpUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.utils.WxCryptUtils;
import com.ehome.common.utils.http.HttpUtils;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.domain.dto.WxLoginDTO;
import com.ehome.oc.mapper.WxUserMapper;
import com.ehome.oc.service.IWxUserService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class WxUserServiceImpl implements IWxUserService {
    private static final Logger log = LoggerFactory.getLogger(WxUserServiceImpl.class);

    @Value("${wechat.appid}")
    private String appid;

    @Value("${wechat.secret}")
    private String secret;

    @Autowired
    private WxUserMapper wxUserMapper;

    @Autowired
    private HttpServletRequest request;

    @Override
    @Transactional
    public WxUser wxLogin(WxLoginDTO loginDTO) {
        try {
            String code = loginDTO.getCode();
            String openid = loginDTO.getOpenid();
            String unionid = loginDTO.getUnionid();
            String sessionKey = loginDTO.getSessionKey();

            // 如果提供了加密数据，尝试解密获取完整用户信息
            JSONObject decryptedUserInfo = null;
            if (StringUtils.isNotEmpty(loginDTO.getEncryptedData()) &&
                StringUtils.isNotEmpty(loginDTO.getIv()) &&
                StringUtils.isNotEmpty(sessionKey)) {
                try {
                    String decryptedData = WxCryptUtils.decrypt(sessionKey, loginDTO.getEncryptedData(), loginDTO.getIv());
                    decryptedUserInfo = JSON.parseObject(decryptedData);
                    log.info("成功解密用户信息: {}", decryptedUserInfo.toJSONString());
                } catch (Exception e) {
                    log.warn("解密用户信息失败，将使用默认信息: {}", e.getMessage());
                }
            }

            // 获取真实IP
            String ip = IpUtils.getIpAddr(request);

            // 查询用户是否存在
            WxUser wxUser = selectWxUserByOpenid(openid);
            boolean isFirstLogin = false;

            if (wxUser == null) {
                // 新用户，自动注册
                isFirstLogin = true;
                wxUser = new WxUser();
                wxUser.setOpenId(openid);
                wxUser.setUnionId(unionid);
                wxUser.setSessionKey(sessionKey);
                // 如果提供了手机号，直接设置
                if (StringUtils.isNotEmpty(loginDTO.getPhoneNumber())) {
                    wxUser.setMobile(loginDTO.getPhoneNumber());
                }
                // 使用解密后的用户信息或默认值
                if (decryptedUserInfo != null) {
                    wxUser.setNickName(decryptedUserInfo.getString("nickName"));
                    wxUser.setAvatarUrl(decryptedUserInfo.getString("avatarUrl"));
                    wxUser.setGender(String.valueOf(decryptedUserInfo.getIntValue("gender")));
                } else {
                    wxUser.setNickName("微信用户");
                }
                wxUser.setStatus("0");
                wxUser.setLoginIp(ip);
                wxUser.setLoginDate(new Date());
                wxUserMapper.insertWxUser(wxUser);
            } else {
                if (unionid != null && !unionid.equals(wxUser.getUnionId())) {
                    wxUser.setUnionId(unionid);
                }
                if (sessionKey != null && !sessionKey.equals(wxUser.getSessionKey())) {
                    wxUser.setSessionKey(sessionKey);
                }
                wxUser.setLoginIp(ip);
                wxUser.setLoginDate(new Date());
                wxUserMapper.updateWxUser(wxUser);
            }

            // 设置首次登录标识
            wxUser.setIsFirstLogin(isFirstLogin);

            Record wxLoginLog = new Record();
            wxLoginLog.set("user_id", wxUser.getUserId());
            wxLoginLog.set("mobile", wxUser.getMobile());
            wxLoginLog.set("login_ip", ip);
            wxLoginLog.set("login_date", new Date());
            wxLoginLog.set("user_agent", request.getHeader("User-Agent"));
            Db.save("wx_login_log","log_id", wxLoginLog);

            return wxUser;
        } catch (Exception e) {
            log.error("微信登录失败", e);
            throw new ServiceException("微信登录失败: " + e.getMessage());
        }
    }

    @Override
    public WxUser selectWxUserById(Long userId) {
        return wxUserMapper.selectWxUserById(userId);
    }

    @Override
    public int updateWxUser(WxUser user) {
        return wxUserMapper.updateWxUser(user);
    }

    @Override
    public WxUser selectWxUserByOpenid(String openid) {
        return wxUserMapper.selectWxUserByOpenid(openid);
    }

    /**
     * 根据code获取微信用户信息（openid、unionid、session_key）
     */
    private JSONObject getWxUserInfoByCode(String code) {
        try {
            // 微信登录凭证校验接口
            String url = String.format("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                    appid, secret, code);

            String response = HttpUtils.sendGet(url);
            JSONObject jsonObject = JSON.parseObject(response);

            // 判断是否成功
            if (jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode") != 0) {
                log.error("获取微信用户信息失败: {}", response);
                return null;
            }

            return jsonObject;
        } catch (Exception e) {
            log.error("调用微信接口失败", e);
            return null;
        }
    }

    @Override
    public void updateUserLoginInfo(Long userId, String loginIp) {
        try {
            WxUser wxUser = new WxUser();
            wxUser.setUserId(userId);
            wxUser.setLoginIp(loginIp);
            wxUser.setLoginDate(new Date());
            wxUserMapper.updateWxUser(wxUser);
            log.info("更新用户登录信息成功，用户ID: {}, 登录IP: {}", userId, loginIp);
        } catch (Exception e) {
            log.error("更新用户登录信息失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
        }
    }

    @Override
    public WxUser checkUserByCode(String code) {
        try {
            // 调用微信接口获取用户信息
            JSONObject wxUserInfo = getWxUserInfoByCode(code);
            if (wxUserInfo == null) {
                log.warn("获取微信用户信息失败，code: {}", code);
                return null;
            }

            String openid = wxUserInfo.getString("openid");
            if (StringUtils.isEmpty(openid)) {
                log.warn("微信返回的openid为空，code: {}", code);
                return null;
            }

            // 查询用户是否存在
            WxUser wxUser = selectWxUserByOpenid(openid);
            log.info("检查用户是否存在: openid={}, 用户存在={}", openid, wxUser != null);

            return wxUser;
        } catch (Exception e) {
            log.error("检查用户失败，code: {}, 错误: {}", code, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> checkUserWithWxInfo(String code) {
        try {
            // 调用微信接口获取用户信息
            JSONObject wxUserInfo = getWxUserInfoByCode(code);
            if (wxUserInfo == null) {
                log.warn("获取微信用户信息失败，code: {}", code);
                throw new RuntimeException("获取微信用户信息失败");
            }

            String openid = wxUserInfo.getString("openid");
            String unionid = wxUserInfo.getString("unionid");
            String sessionKey = wxUserInfo.getString("session_key");

            if (StringUtils.isEmpty(openid)) {
                log.warn("微信返回的openid为空，code: {}", code);
                throw new RuntimeException("微信返回的openid为空");
            }

            // 查询用户是否存在
            WxUser existingUser = selectWxUserByOpenid(openid);
            boolean userExists = existingUser != null;

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("userExists", userExists);
            result.put("openid", openid);
            result.put("unionid", unionid);
            result.put("sessionKey", sessionKey);

            if (userExists) {
                result.put("userId", existingUser.getUserId());
                result.put("nickName", existingUser.getNickName());
                result.put("avatarUrl", existingUser.getAvatarUrl());
                result.put("mobile", existingUser.getMobile());
            }

            log.info("检查用户完成: openid={}, 用户存在={}", openid, userExists);
            return result;

        } catch (Exception e) {
            log.error("检查用户失败，code: {}, 错误: {}", code, e.getMessage(), e);
            throw new RuntimeException("检查用户失败: " + e.getMessage(), e);
        }
    }

}