<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('公共文档库')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
    <style type="text/css">
        #treeSearchInput{width: 170px;}
    </style>
</head>
<body class="gray-bg">   
    <div class="ui-layout-west">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    <i class="fa fa-folder-open"></i> 文档分类
                </div>
                <div class="box-tools pull-right">
                    <a type="button" class="btn btn-box-tool" href="javascript:void(0)" onclick="addFolder()" title="新增文件夹"><i class="fa fa-plus"></i></a>
                    <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新目录"><i class="fa fa-refresh"></i></button>
                </div>
            </div>
            <div class="ui-layout-content">
                <div style="padding: 0px;padding-bottom: 5px;">
                    <div class="input-group" style="margin-bottom: 0px;">
                        <input type="text" id="treeSearchInput" placeholder="搜索文件夹" class="form-control form-control-sm" onkeypress="if(event.keyCode==13) searchTree()"/>
                        <span class="input-group-btn">
                            <button class="btn btn-sm btn-default" type="button" onclick="searchTree()"><i class="fa fa-search"></i></button>
                            <button class="btn btn-sm btn-default" type="button" onclick="clearTreeSearch()"><i class="fa fa-times"></i></button>
                        </span>
                    </div>
                </div>
                <div id="folderTree" class="ztree"></div>
            </div>
        </div>
    </div>

    <div class="ui-layout-center">
        <div class="container-div">
            <div class="row">
                <!-- 搜索区域 -->
                <div class="col-sm-12 search-collapse">
                    <form id="formId">
                        <input type="hidden" name="folderId" id="folderId"/>
                        <div class="select-list">
                            <ul>
                                <li>
                                    <label>当前位置：</label>
                                    <input type="text" name="currentPath" placeholder="根目录" readonly style="background-color: #f5f5f5;"/>
                                </li>

                                <li>
                                    <label>文件名：</label>
                                    <input type="text" name="fileName" placeholder="请输入文件名" onkeypress="if(event.keyCode==13) $.table.search()"/>
                                </li>

                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm ml-10" onclick="resetSearch()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>

                <!-- 工具栏和表格区域 -->
                <div class="btn-group-sm" id="toolbar" role="group">
                    <a class="btn btn-success" onclick="uploadFile()">
                        <i class="fa fa-upload"></i> 上传文件
                    </a>
                    <a class="btn btn-info ml-10" onclick="moveFiles()">
                        <i class="fa fa-arrows"></i> 移动文件
                    </a>
                    <a class="btn btn-danger ml-10 multiple disabled" onclick="$.operate.removeAll()">
                        <i class="fa fa-remove"></i> 删除
                    </a>
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 右键菜单 -->
    <div id="rightClickMenu" class="dropdown-menu" style="position: absolute; display: none; z-index: 9999; min-width: 120px; padding: 5px 0; background: white; border: 1px solid #ccc; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.15);">
        <ul class="list-unstyled" style="margin: 0;">
            <li><a href="javascript:void(0)" onclick="addSubFolder()" style="display: block; padding: 8px 15px; color: #333; text-decoration: none; font-size: 12px;"><i class="fa fa-plus" style="margin-right: 5px;"></i>新增子文件夹</a></li>
            <li><a href="javascript:void(0)" onclick="editFolder()" style="display: block; padding: 8px 15px; color: #333; text-decoration: none; font-size: 12px;"><i class="fa fa-edit" style="margin-right: 5px;"></i>编辑文件夹</a></li>
            <li><a href="javascript:void(0)" onclick="deleteFolder()" style="display: block; padding: 8px 15px; color: #333; text-decoration: none; font-size: 12px;"><i class="fa fa-trash" style="margin-right: 5px;"></i>删除文件夹</a></li>
            <li class="divider" style="height: 1px; margin: 5px 0; background-color: #e5e5e5;"></li>
            <li><a href="javascript:void(0)" onclick="uploadToFolder()" style="display: block; padding: 8px 15px; color: #333; text-decoration: none; font-size: 12px;"><i class="fa fa-upload" style="margin-right: 5px;"></i>上传文件到此文件夹</a></li>
        </ul>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js" />
    <th:block th:include="include :: ztree-js" />
    <script th:inline="javascript">
        var prefix = ctx + "document";
        var currentFolderId = "";
        var currentFolderName = "根目录";
        var currentRightClickNode = null;



        $(function() {
            var panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 250, resizeWithWindow: false });

            loadFolderTree();
            
            var options = {
                url: prefix + "/fileList",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: ctx + "file/remove",
                modalName: "文件",
                limit: 20,

                columns: [{
                    checkbox: true
                },
                {
                    field: 'file_id',
                    title: '文件ID',
                    visible: false
                },
                {
                    field: 'original_name',
                    title: '文件名',
                    formatter: function(value, row, index) {
                        var downloadUrl = ctx + 'attachment/download/' + row.file_id;
                        return '<a href="' + downloadUrl + '" target="_blank" title="点击预览">' + value + '</a>';
                    }
                },
                {
                    field: 'file_type',
                    title: '文件类型',
                    formatter: function(value, row, index) {
                        return value ? value.toUpperCase() : '-';
                    }
                },
                {
                    field: 'file_size',
                    title: '文件大小',
                    formatter: function(value, row, index) {
                        return formatFileSize(value);
                    }
                },
                {
                    field: 'upload_user',
                    title: '上传用户'
                },
                {
                    field: 'create_time',
                    title: '上传时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        var downloadUrl = ctx + 'attachment/download/' + row.file_id;
                        actions.push('<a target="_blank" class="btn btn-info btn-xs" href="' + downloadUrl + '" download="' + row.original_name + '"><i class="fa fa-download"></i>下载</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" onclick="$.operate.remove(\'' + row.file_id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 加载文件夹树
        function loadFolderTree() {
            $.ajax({
                url: prefix + "/folderTree",
                type: "POST",
                success: function(res) {
                    if (res.code == 0) {
                        var setting = {
                            data: {
                                simpleData: {
                                    enable: true,
                                    idKey: "id",
                                    pIdKey: "pId",
                                    rootPId: null
                                }
                            },
                            view: {
                                showIcon: true,
                                selectedMulti: false
                            },
                            callback: {
                                onClick: function(event, treeId, treeNode) {
                                    currentFolderId = treeNode.id;
                                    currentFolderName = treeNode.name;
                                    $("#folderId").val(currentFolderId);
                                    $("input[name='currentPath']").val(getFolderPath(treeNode));
                                    $.table.search();
                                },
                                onRightClick: function(event, treeId, treeNode) {
                                    if (!treeNode) return;
                                    var treeObj = $.fn.zTree.getZTreeObj(treeId);
                                    treeObj.selectNode(treeNode);
                                    showRightClickMenu(event, treeNode);
                                }
                            }
                        };
                        
                        // 添加根目录节点
                        var treeData = [{
                            id: "",
                            pId: null,
                            name: "根目录",
                            type: "folder",
                            open: true
                        }];

                        if (res.data && res.data.length > 0) {
                            // 设置所有节点默认展开
                            res.data.forEach(function(node) {
                                node.open = true;
                            });
                            treeData = treeData.concat(res.data);
                        }

                        var treeObj = $.fn.zTree.init($("#folderTree"), setting, treeData);
                        // 展开所有节点
                        treeObj.expandAll(true);
                    } else {
                        $.modal.alertError("加载文件夹树失败：" + res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.alertError("加载文件夹树失败：" + error);
                }
            });
        }

        // 获取文件夹路径
        function getFolderPath(treeNode) {
            if (!treeNode || treeNode.id === "") {
                return "根目录";
            }

            var path = treeNode.name;
            var parent = treeNode.getParentNode();
            while (parent && parent.id !== "") {
                path = parent.name + " / " + path;
                parent = parent.getParentNode();
            }
            return "根目录 / " + path;
        }



        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }



        // 重置搜索
        function resetSearch() {
            $("#formId")[0].reset();
            $("#folderId").val(currentFolderId);
            $("input[name='currentPath']").val(currentFolderName);
            $.table.search();
        }

        // 上传文件
        function uploadFile() {
            FileAccessUtils.uploadFiles({
                fileType: 'all',
                source: 'document',
                bucketType: 'public',
                folderId: currentFolderId,
                communityId: '0'
            }, function(uploadedFiles) {
                if (uploadedFiles && uploadedFiles.length > 0) {
                    $.modal.msgSuccess('成功上传 ' + uploadedFiles.length + ' 个文件');
                    $.table.refresh();
                } else {
                    $.modal.msgWarning('没有上传任何文件');
                }
            });
        }

        // 移动文件
        function moveFiles() {
            var rows = $.table.selectColumns("file_id");
            if (rows.length == 0) {
                $.modal.alertWarning("请选择要移动的文件");
                return;
            }

            var fileIds = rows.join(",");
            var url = ctx + "document/moveFileDialog?fileIds=" + fileIds;
            $.modal.open("移动文件", url, '400', '500');
        }

        // 展开所有节点
        $('#btnExpand').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("folderTree");
            treeObj.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        // 折叠所有节点
        $('#btnCollapse').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("folderTree");
            treeObj.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });

        // 刷新树
        $('#btnRefresh').click(function() {
            loadFolderTree();
        });

        // 搜索树节点
        function searchTree() {
            var keyword = $("#treeSearchInput").val();
            var treeObj = $.fn.zTree.getZTreeObj("folderTree");
            if (!treeObj) return;

            if (keyword) {
                var allNodes = treeObj.getNodes();
                var nodesToShow = [];

                function searchNodes(nodes) {
                    for (var i = 0; i < nodes.length; i++) {
                        var node = nodes[i];
                        if (node.name && node.name.indexOf(keyword) !== -1) {
                            nodesToShow.push(node);
                            var parent = node.getParentNode();
                            while (parent) {
                                if (nodesToShow.indexOf(parent) === -1) {
                                    nodesToShow.push(parent);
                                }
                                parent = parent.getParentNode();
                            }
                        }
                        if (node.children && node.children.length > 0) {
                            searchNodes(node.children);
                        }
                    }
                }

                searchNodes(allNodes);

                if (nodesToShow.length > 0) {
                    function hideAllNodes(nodes) {
                        for (var i = 0; i < nodes.length; i++) {
                            treeObj.hideNode(nodes[i]);
                            if (nodes[i].children && nodes[i].children.length > 0) {
                                hideAllNodes(nodes[i].children);
                            }
                        }
                    }
                    hideAllNodes(allNodes);

                    for (var i = 0; i < nodesToShow.length; i++) {
                        treeObj.showNode(nodesToShow[i]);
                    }

                    var firstMatchNode = null;
                    for (var i = 0; i < nodesToShow.length; i++) {
                        var node = nodesToShow[i];
                        if (node.name && node.name.indexOf(keyword) !== -1) {
                            if (!firstMatchNode) {
                                firstMatchNode = node;
                            }
                            var parent = node.getParentNode();
                            while (parent) {
                                treeObj.expandNode(parent, true, false, false);
                                parent = parent.getParentNode();
                            }
                        }
                    }

                    if (firstMatchNode) {
                        treeObj.selectNode(firstMatchNode, true);
                    }
                } else {
                    $.modal.alertWarning("未找到匹配的文件夹");
                }
            } else {
                var allNodes = treeObj.getNodes();
                function showAllNodes(nodes) {
                    for (var i = 0; i < nodes.length; i++) {
                        treeObj.showNode(nodes[i]);
                        if (nodes[i].children && nodes[i].children.length > 0) {
                            showAllNodes(nodes[i].children);
                        }
                    }
                }
                showAllNodes(allNodes);
                treeObj.expandAll(false);
            }
        }

        // 清空树搜索
        function clearTreeSearch() {
            $("#treeSearchInput").val("");
            searchTree();
        }

        // 新增文件夹
        function addFolder() {
            var url = ctx + "document/folder/add";
            $.modal.open("新增文件夹", url, '500', '400');
        }

        // 显示右键菜单
        function showRightClickMenu(event, treeNode) {
            currentRightClickNode = treeNode;
            var menu = $("#rightClickMenu");

            menu.css({
                left: event.pageX + "px",
                top: event.pageY + "px",
                display: "block"
            });

            event.preventDefault();
            return false;
        }

        // 隐藏右键菜单
        function hideRightClickMenu() {
            $("#rightClickMenu").hide();
        }

        // 点击其他地方隐藏右键菜单
        $(document).click(function() {
            hideRightClickMenu();
        });

        // 新增子文件夹
        function addSubFolder() {
            if (!currentRightClickNode) return;
            var parentId = currentRightClickNode.id === "" ? null : currentRightClickNode.id;
            var url = ctx + "document/folder/add?parentId=" + (parentId || "");
            $.modal.open("新增子文件夹", url, '500', '400');
            hideRightClickMenu();
        }

        // 编辑文件夹
        function editFolder() {
            if (!currentRightClickNode || currentRightClickNode.id === "") {
                $.modal.alertWarning("根目录不能编辑");
                return;
            }
            var url = ctx + "document/folder/edit/" + currentRightClickNode.id;
            $.modal.open("编辑文件夹", url, '500', '400');
            hideRightClickMenu();
        }

        // 删除文件夹
        function deleteFolder() {
            if (!currentRightClickNode || currentRightClickNode.id === "") {
                $.modal.alertWarning("根目录不能删除");
                return;
            }

            $.modal.confirm("确定删除文件夹 '" + currentRightClickNode.name + "' 吗？", function() {
                $.ajax({
                    url: ctx + "document/folder/remove",
                    type: "POST",
                    data: { ids: currentRightClickNode.id },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("删除成功");
                            loadFolderTree();
                            if (currentFolderId === currentRightClickNode.id) {
                                currentFolderId = "";
                                currentFolderName = "根目录";
                                $("#folderId").val("");
                                $("input[name='currentPath']").val("根目录");
                                $.table.search();
                            }
                        } else {
                            $.modal.alertError(res.msg);
                        }
                    }
                });
            });
            hideRightClickMenu();
        }

        // 上传文件到文件夹
        function uploadToFolder() {
            if (!currentRightClickNode) return;
            var folderId = currentRightClickNode.id === "" ? "" : currentRightClickNode.id;
            var folderName = currentRightClickNode.name;

            FileAccessUtils.uploadFiles({
                fileType: 'all',
                source: 'document',
                bucketType: 'public',
                folderId: folderId,
                communityId: '0'
            }, function(uploadedFiles) {
                if (uploadedFiles && uploadedFiles.length > 0) {
                    $.modal.msgSuccess('成功上传 ' + uploadedFiles.length + ' 个文件到 ' + folderName);
                    $.table.refresh();
                } else {
                    $.modal.msgWarning('没有上传任何文件');
                }
            });
            hideRightClickMenu();
        }
    </script>
</body>
</html>
