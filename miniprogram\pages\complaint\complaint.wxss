/* 页面整体样式 */
page {
  background: linear-gradient(180deg, #f0f7ff 0%, #f5f7fa 100%);
}

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  padding: 40rpx 30rpx 60rpx;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  transform: rotate(-15deg);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 2;
}

.header-title {
  flex: 1;
}

.title-main {
  display: block;
  font-size: 44rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.title-sub {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.header-action {
  margin-left: 20rpx;
}

.history-link {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50rpx;
  color: #ffffff;
  font-size: 26rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.history-link:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.95);
}

.history-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

/* 表单容器样式 */
.form-container {
  padding: 0 30rpx;
  margin-top: -30rpx;
  position: relative;
  z-index: 1;
}

/* 卡片样式 */
.card-section {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.card-section:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(24, 144, 255, 0.12);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(90deg, #fafbff 0%, #ffffff 100%);
}

.card-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  width: 40rpx;
  text-align: center;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

.card-subtitle {
  font-size: 24rpx;
  color: #999999;
  margin-left: auto;
}

.card-content {
  padding: 30rpx;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 24rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 24rpx;
  display: block;
  padding: 0;
}

.required {
  color: #ff4757;
  margin-left: 4rpx;
  font-size: 28rpx;
}

.label-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  margin-bottom: 24rpx;
}

/* 类型选择器样式 */
.type-selector {
  margin-bottom: 16rpx !important;
}

.type-picker {
  display: block;
  width: 100%;
}

/* Vant Field 样式 - 使用原始样式，只调整间距 */
.van-field {
  margin-bottom: 16rpx !important;
}

.van-field:last-child {
  margin-bottom: 0 !important;
}

/* 移除自定义uploader样式，使用vant原生样式 */

/* 提交容器样式 */
.submit-container {
  padding: 30rpx;
  background: transparent;
  position: sticky;
  bottom: 0;
  z-index: 100;
}

/* 提交按钮样式 */
.submit-btn {
  width: 100% !important;
  height: 100rpx !important;
  line-height: 100rpx !important;
  font-size: 36rpx !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  border-radius: 50rpx !important;
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.3) !important;
  border: none !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.submit-btn:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.4) !important;
}

.submit-btn:active::before {
  left: 100%;
}

/* 单选按钮组容器 */
.van-radio-group {
  background: #f8f9ff;
  border-radius: 16rpx;
  border: 1rpx solid #e6f0ff;
  overflow: hidden;
}

/* 单选按钮样式优化 */
.van-radio {
  padding: 24rpx 20rpx !important;
  transition: background-color 0.2s ease !important;
}

.van-radio:active {
  background-color: rgba(24, 144, 255, 0.05) !important;
}

.van-radio__icon--checked {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.van-radio__label {
  color: #333333 !important;
  font-size: 32rpx !important;
  margin-left: 16rpx !important;
}