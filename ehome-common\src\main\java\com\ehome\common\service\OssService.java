package com.ehome.common.service;

import com.ehome.common.config.OssConfig;
import com.ehome.common.utils.oss.OssUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * OSS服务类
 * 
 * <AUTHOR>
 */
@Service
public class OssService {
    
    private static final Logger log = LoggerFactory.getLogger(OssService.class);
    
    @Autowired
    private OssConfig ossConfig;
    
    @Autowired
    private OssUtils ossUtils;
    
    /**
     * 上传文件结果
     */
    public static class UploadResult {
        private boolean success;
        private String objectKey;
        private String url;
        private String errorMessage;
        
        public UploadResult(boolean success, String objectKey, String url, String errorMessage) {
            this.success = success;
            this.objectKey = objectKey;
            this.url = url;
            this.errorMessage = errorMessage;
        }
        
        public static UploadResult success(String objectKey, String url) {
            return new UploadResult(true, objectKey, url, null);
        }
        
        public static UploadResult failure(String errorMessage) {
            return new UploadResult(false, null, null, errorMessage);
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getObjectKey() { return objectKey; }
        public String getUrl() { return url; }
        public String getErrorMessage() { return errorMessage; }
    }
    
    /**
     * 上传文件到OSS
     * 
     * @param file 上传的文件
     * @return 上传结果
     */
    public UploadResult uploadFile(MultipartFile file) {
        return uploadFile(file, null);
    }
    
    /**
     * 上传文件到OSS
     *
     * @param file 上传的文件
     * @param folder 文件夹路径
     * @return 上传结果
     */
    public UploadResult uploadFile(MultipartFile file, String folder) {
        return uploadFile(file, folder, false);
    }

    /**
     * 上传文件到OSS
     *
     * @param file 上传的文件
     * @param folder 文件夹路径
     * @param isPublic 是否上传到公共Bucket
     * @return 上传结果
     */
    public UploadResult uploadFile(MultipartFile file, String folder, boolean isPublic) {
        // 检查OSS是否启用
        if (!ossConfig.isEnabled()) {
            log.debug("OSS功能未启用，跳过上传");
            return UploadResult.failure("OSS功能未启用");
        }

        // 检查配置是否有效
        if (!ossConfig.isConfigValid()) {
            log.error("OSS配置无效，无法上传文件");
            return UploadResult.failure("OSS配置无效");
        }

        if (isPublic && !ossConfig.isPublicBucketConfigValid()) {
            log.error("公共OSS Bucket配置无效，无法上传文件");
            return UploadResult.failure("公共OSS Bucket配置无效");
        }

        try {
            // 上传文件
            String objectKey = ossUtils.uploadFile(file, folder, isPublic);
            String url = ossUtils.getFileUrl(objectKey, isPublic);

            log.info("文件上传到OSS成功: {} -> {} ({})", file.getOriginalFilename(), objectKey,
                    isPublic ? "公共Bucket" : "私有Bucket");
            return UploadResult.success(objectKey, url);

        } catch (Exception e) {
            log.error("文件上传到OSS失败: {}", e.getMessage(), e);
            return UploadResult.failure(e.getMessage());
        }
    }
    
    /**
     * 删除OSS文件
     * 
     * @param objectKey 文件键名
     * @return 是否删除成功
     */
    public boolean deleteFile(String objectKey,boolean isPublic) {
        if (!ossConfig.isEnabled()) {
            log.debug("OSS功能未启用，跳过删除");
            return false;
        }
        
        try {
            return ossUtils.deleteFile(objectKey,isPublic);
        } catch (Exception e) {
            log.error("删除OSS文件失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取文件访问URL（预签名URL）
     *
     * @param objectKey 文件键名
     * @return 预签名访问URL
     */
    public String getFileUrl(String objectKey) {
        if (!ossConfig.isEnabled()) {
            return null;
        }

        try {
            return ossUtils.getFileUrl(objectKey);
        } catch (Exception e) {
            log.error("获取OSS文件URL失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据OSS Key生成预签名URL
     *
     * @param objectKey OSS文件键名
     * @param expiration 过期时间（秒），如果为null则使用默认配置
     * @return 预签名URL
     */
    public String generatePresignedUrl(String ossUrl,String objectKey, Long expiration) {
        if (!ossConfig.isEnabled()) {
            log.debug("OSS功能未启用");
            return null;
        }

        try {
            long exp = expiration != null ? expiration : ossConfig.getUrlExpiration();
            if(ossUrl!=null&&ossUrl.contains("ehome-public")){
                return ossUtils.getPresignedUrl(objectKey, exp,true);
            }else{
                return ossUtils.getPresignedUrl(objectKey, exp);
            }
        } catch (Exception e) {
            log.error("生成预签名URL失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据OSS Key生成预签名URL（使用默认过期时间）
     *
     * @param objectKey OSS文件键名
     * @return 预签名URL
     */
    public String generatePresignedUrl(String ossUrl,String objectKey) {
        return generatePresignedUrl(ossUrl,objectKey, null);
    }
    
    /**
     * 检查OSS服务是否可用
     *
     * @return 是否可用
     */
    public boolean isAvailable() {
        return ossConfig.isEnabled() && ossConfig.isConfigValid();
    }

    /**
     * 获取URL过期时间配置
     *
     * @return URL过期时间（秒）
     */
    public long getUrlExpiration() {
        return ossConfig.getUrlExpiration();
    }
}
