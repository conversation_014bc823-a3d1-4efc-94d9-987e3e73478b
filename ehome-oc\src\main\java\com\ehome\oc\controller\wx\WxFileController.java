package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.config.RuoYiConfig;
import com.ehome.common.config.ServerConfig;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.service.OssService;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.ServletUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.file.FileUploadUtils;
import com.ehome.common.utils.file.FileUtils;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.oc.domain.WxFileUploadResult;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 小程序文件处理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/wx/file")
public class WxFileController extends BaseWxController {


    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private OssService ossService;

    private static final String FILE_DELIMETER = ",";


    @PostMapping("/getAttachments")
    public AjaxResult getAttachments() {
        try {
            JSONObject params = getParams();
            String businessId = params.getString("businessId");
            String businessType = params.getString("businessType");
            String source = params.getString("source");
            if (StringUtils.isEmpty(businessId)) {
                return AjaxResult.error("业务ID不能为空");
            }
            List<Record> attachments = Db.find("SELECT file_id,file_id id, original_name, access_url, file_size, file_type, create_time FROM eh_file_info WHERE  business_id = ? AND status = '0' ORDER BY create_time ASC", businessId);
            return AjaxResult.success(recordToMap(attachments));
        } catch (Exception e) {
            logger.error("获取附件失败: " + e.getMessage(), e);
            return AjaxResult.error("获取附件失败: " + e.getMessage());
        }
    }

    /**
     * 小程序文件上传接口（单个）
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) {
        try {
            String source = getRequest().getParameter("source");
            String businessId = getRequest().getParameter("businessId");
            String bucketType = getRequest().getParameter("bucketType");
            boolean isPublic = "public".equalsIgnoreCase(bucketType);
            if("avatar".equals(source)){
                businessId = getCurrentUser() != null ? getCurrentUser().getUserId().toString() : "";
            }

            if (file == null || file.isEmpty()) {
                return AjaxResult.error("上传文件不能为空");
            }

            // 处理文件上传
            WxFileUploadResult result = processWxFileUpload(file, source, businessId, isPublic);

            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", result.getAccessUrl());
            ajax.put("fileId", result.getFileId());
            ajax.put("fileName", result.getFileName());
            ajax.put("newFileName", FileUtils.getName(result.getFileName()));
            ajax.put("originalFilename", result.getOriginalFilename());
            ajax.put("storageType", result.getStorageType());
            return ajax;

        } catch (Exception e) {
            logger.error("小程序文件上传失败", e);
            return AjaxResult.error("上传失败: " + e.getMessage());
        }
    }

    /**
     * 小程序文件上传接口（多个）
     */
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(@RequestParam("files") List<MultipartFile> files) {
        try {
            String source = getRequest().getParameter("source");
            String businessId = getRequest().getParameter("businessId");
            String bucketType = getRequest().getParameter("bucketType");
            boolean isPublic = "public".equalsIgnoreCase(bucketType);

            if (files == null || files.isEmpty()) {
                return AjaxResult.error("上传文件不能为空");
            }

            List<String> urls = new ArrayList<>();
            List<String> fileIds = new ArrayList<>();
            List<String> fileNames = new ArrayList<>();
            List<String> newFileNames = new ArrayList<>();
            List<String> originalFilenames = new ArrayList<>();

            for (MultipartFile file : files) {
                if (!file.isEmpty()) {
                    WxFileUploadResult result = processWxFileUpload(file, source, businessId, isPublic);

                    urls.add(result.getFinalUrl());
                    fileIds.add(result.getFileId());
                    fileNames.add(result.getFileName());
                    newFileNames.add(FileUtils.getName(result.getFileName()));
                    originalFilenames.add(result.getOriginalFilename());
                }
            }

            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileIds", StringUtils.join(fileIds, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
            return ajax;

        } catch (Exception e) {
            logger.error("小程序批量文件上传失败", e);
            return AjaxResult.error("批量上传失败: " + e.getMessage());
        }
    }

    /**
     * 统一文件访问接口 - 通过文件ID获取访问URL
     * 重构版本：优化错误处理和日志记录，添加下载统计
     */
    @PostMapping("/access")
    public AjaxResult getFileAccessUrl() {
        long startTime = System.currentTimeMillis();
        String fileId = null;

        try {
            JSONObject params = getParams();
            fileId = params.getString("fileId");

            if (StringUtils.isEmpty(fileId)) {
                logger.warn("文件访问请求缺少fileId参数");
                return AjaxResult.error("文件ID不能为空");
            }

            logger.info("开始处理文件访问请求: fileId={}", fileId);

            // 获取文件信息和访问URL
            FileAccessResult result = getFileInfoAndUrl(fileId);
            if (result == null) {
                logger.warn("文件不存在或已删除: fileId={}", fileId);
                return AjaxResult.error("文件不存在或已删除");
            }

            // 记录下载统计
            updateWxDownloadStats(fileId);

            long totalTime = System.currentTimeMillis() - startTime;
            logger.info("文件访问URL生成成功: fileId={}, actualStorageType={}, 总耗时={}ms",
                       fileId, result.actualStorageType, totalTime);

            // 根据文件类型提供预览建议
            String fileType = result.fileInfo.getStr("file_type");
            String previewType = determinePreviewType(fileType);

            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", result.accessUrl);
            ajax.put("fileId", fileId);
            ajax.put("fileName", result.fileInfo.getStr("original_name"));
            ajax.put("fileType", fileType);
            ajax.put("mimeType", result.fileInfo.getStr("mime_type"));
            ajax.put("storageType", result.actualStorageType);
            ajax.put("fileSize", result.fileInfo.getLong("file_size"));
            ajax.put("previewType", previewType);
            ajax.put("canPreview", isPreviewable(fileType));
            return ajax;

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            logger.error("文件访问失败: fileId={}, 耗时={}ms, error={}", fileId, totalTime, e.getMessage(), e);
            return AjaxResult.error("文件访问失败: " + e.getMessage());
        }
    }

    /**
     * 文件下载接口 - 通过文件ID下载文件
     * 重构版本：使用共同方法优化代码结构
     */
    @GetMapping("/download/{fileId}")
    public void downloadFile(@PathVariable String fileId, HttpServletResponse response) {
        try {
            if (StringUtils.isEmpty(fileId)) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "文件ID不能为空");
                return;
            }

            // 获取文件信息和访问URL
            FileAccessResult result = getFileInfoAndUrl(fileId);
            if (result == null) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在或已删除");
                return;
            }

            String originalName = result.fileInfo.getStr("original_name");
            String mimeType = result.fileInfo.getStr("mime_type");
            String absolutePath = result.fileInfo.getStr("absolute_path");

            // 设置响应头
            response.setContentType(StringUtils.isNotEmpty(mimeType) ? mimeType : MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, originalName);

            // 根据存储类型处理下载
            if ("oss".equals(result.actualStorageType)) {
                // OSS文件，直接重定向到预签名URL
                response.sendRedirect(result.accessUrl);
                updateWxDownloadStats(fileId);
                return;
            }

            // 本地文件下载
            if (StringUtils.isNotEmpty(absolutePath)) {
                try {
                    File file = new File(absolutePath);
                    if (file.exists() && file.isFile()) {
                        FileUtils.writeBytes(absolutePath, response.getOutputStream());
                        updateWxDownloadStats(fileId);
                    } else {
                        // 文件不存在，重定向到访问URL
                        logger.warn("本地文件不存在，重定向到访问URL: {} -> {}", absolutePath, result.accessUrl);
                        response.sendRedirect(encodeRedirectUrl(result.accessUrl));
                    }
                } catch (Exception e) {
                    logger.error("本地文件访问失败: {}", absolutePath, e);
                    // 降级到访问URL重定向
                    logger.warn("本地文件访问异常，重定向到访问URL: {}", result.accessUrl);
                    response.sendRedirect(encodeRedirectUrl(result.accessUrl));
                }
            } else {
                // 没有本地路径，直接重定向到访问URL
                logger.info("使用访问URL进行重定向: {}", result.accessUrl);
                response.sendRedirect(encodeRedirectUrl(result.accessUrl));
            }

        } catch (Exception e) {
            logger.error("文件下载失败: {}", fileId, e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "文件下载失败");
            } catch (Exception ignored) {
            }
        }
    }

    /**
     * 处理小程序文件上传的核心方法
     */
    private WxFileUploadResult processWxFileUpload(MultipartFile file, String source, String businessId, boolean isPublic) throws Exception {
        // 设置上传路径
        String filePath = RuoYiConfig.getUploadPath();
        if (!StringUtils.isEmpty(source)) {
            filePath = filePath + File.separator + source;
        }

        // 上传到本地并获取绝对路径
        String fileName = FileUploadUtils.upload(filePath, file);
        String accessUrl = serverConfig.getUrl() + "/fv/download/";

        // 直接从File对象获取绝对路径，简化复杂的字符串处理
        String extractedFileName = FileUploadUtils.extractFilename(file);
        File absoluteFile = FileUploadUtils.getAbsoluteFile(filePath, extractedFileName);
        String absolutePath = absoluteFile.getAbsolutePath();

        // 默认存储类型为local，总是先上传到本地
        String storageType = "local";
        String ossUrl = null;
        String ossKey = null;

        // 尝试上传到OSS
        if (ossService.isAvailable()) {
            try {
                OssService.UploadResult ossResult = ossService.uploadFile(file, source, isPublic);
                if (ossResult.isSuccess()) {
                    ossUrl = ossResult.getUrl();
                    ossKey = ossResult.getObjectKey();
                    storageType = "both"; // 本地和OSS都有
                    logger.info("小程序文件同步上传到OSS成功: {} -> {} ({})", file.getOriginalFilename(), ossKey, isPublic ? "公共Bucket" : "私有Bucket");
                } else {
                    logger.warn("小程序文件上传到OSS失败: {}", ossResult.getErrorMessage());
                }
            } catch (Exception ossException) {
                logger.warn("小程序OSS上传异常: {}", ossException.getMessage(), ossException);
            }
        }

        // 创建上传结果
        WxFileUploadResult result = new WxFileUploadResult(
                fileName, accessUrl, absolutePath, ossUrl, ossKey, storageType,
                file.getOriginalFilename(), file.getSize(),
                FileUploadUtils.getExtension(file), file.getContentType()
        );
        result.setBusinessId(businessId);
        result.setBusinessType(source);
        result.setCommunityId(getCurrentUser() != null ? getCurrentUser().getCommunityId() : "");
        // 保存文件信息到数据库
         saveWxFileInfoToDatabase(result);
        // 方便PC和小程序直接访问
        result.setAccessUrl(result.getAccessUrl()+result.getFileId());
        return result;
    }

    /**
     * 保存小程序文件信息到数据库
     */
    private String saveWxFileInfoToDatabase(WxFileUploadResult result) {
        String fileId = null;
        try {
            String communityId = result.getCommunityId();
            fileId = Seq.getId(Seq.uploadSeqType);
            result.setFileId(fileId);
            Record fileRecord = new Record();
            fileRecord.set("file_id",fileId);
            fileRecord.set("original_name", result.getOriginalFilename());
            fileRecord.set("file_name", FileUtils.getName(result.getFileName()));
            fileRecord.set("file_path", result.getFileName());
            fileRecord.set("absolute_path", result.getAbsolutePath());
            fileRecord.set("access_url", result.getAccessUrl()+fileId);
            fileRecord.set("oss_url", result.getOssUrl());
            fileRecord.set("oss_key", result.getOssKey());
            fileRecord.set("storage_type", result.getStorageType());
            fileRecord.set("file_size", result.getFileSize());
            fileRecord.set("file_size_str", FileUtils.formatFileSize(result.getFileSize()));
            fileRecord.set("file_type", result.getFileType());
            fileRecord.set("mime_type", result.getMimeType());
            fileRecord.set("upload_user", getCurrentUser() != null ? getCurrentUser().getUsername() : "anonymous");
            fileRecord.set("community_id", getCurrentUser()!=null? getCurrentUser().getCommunityId() : communityId);
            fileRecord.set("download_count", 0);
            fileRecord.set("last_download_time", "");
            fileRecord.set("create_time", DateUtils.getTime());
            fileRecord.set("update_time", DateUtils.getTime());
            fileRecord.set("status", "0");
            fileRecord.set("business_type", result.getBusinessType());
            fileRecord.set("business_id", result.getBusinessId());
            Db.save("eh_file_info", "file_id", fileRecord);
        } catch (Exception dbException) {
            logger.error("小程序保存文件信息到数据库失败: " + dbException.getMessage(), dbException);
        }
        return fileId;
    }

    /**
     * 确保URL是完整的HTTP地址
     */
    private String ensureFullUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        if (url.startsWith("http://") || url.startsWith("https://")) {
            return url;
        }
        return serverConfig.getUrl() + url;
    }


    /**
     * 更新小程序文件下载统计
     *
     * @param fileId 文件ID
     */
    private void updateWxDownloadStats(String fileId) {
        try {
            String currentTime = DateUtils.getTime();

            // 更新文件下载次数和最后下载时间
            Db.update("UPDATE eh_file_info SET download_count = download_count + 1, last_download_time = ? WHERE file_id = ?",
                     currentTime, fileId);

            // 记录下载日志
            Record logRecord = new Record();
            logRecord.set("file_id", fileId);
            logRecord.set("user_id", getCurrentUser() != null ? getCurrentUser().getUserId() : "");
            logRecord.set("user_name", getCurrentUser() != null ? getCurrentUser().getMobile() : "anonymous");
            logRecord.set("user_type", "wx_user");
            logRecord.set("download_ip", getRequest().getRemoteAddr());
            logRecord.set("user_agent", getRequest().getHeader("User-Agent"));
            logRecord.set("download_time", currentTime);
            logRecord.set("status", "0");
            logRecord.set("error_msg", "");
            logRecord.set("pms_id", "");
            logRecord.set("community_id", getCurrentUser() != null ? getCurrentUser().getCommunityId() : "");
            logRecord.set("owner_id", getCurrentUser() != null ? getCurrentUser().getOwnerId() : "");
            logRecord.set("house_id", getCurrentUser() != null ? getCurrentUser().getHouseId() : "");
            logRecord.set("house_name", getCurrentUser() != null ? getCurrentUser().getHouseName() : "");

            Db.save("eh_file_download_log", logRecord);

        } catch (Exception e) {
            logger.warn("更新小程序文件下载统计失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 根据文件扩展名确定预览类型
     * @param fileType 文件扩展名
     * @return 预览类型：image, document, video, audio, other
     */
    private String determinePreviewType(String fileType) {
        if (StringUtils.isEmpty(fileType)) {
            return "other";
        }

        String type = fileType.toLowerCase();

        // 图片类型
        if (type.matches("jpg|jpeg|png|gif|webp|bmp|svg|ico|tiff|tif")) {
            return "image";
        }

        // 文档类型
        if (type.matches("pdf|doc|docx|xls|xlsx|ppt|pptx|txt|rtf|odt|ods|odp")) {
            return "document";
        }

        // 视频类型
        if (type.matches("mp4|avi|mov|wmv|flv|mkv|webm|3gp")) {
            return "video";
        }

        // 音频类型
        if (type.matches("mp3|wav|aac|flac|ogg|wma|m4a")) {
            return "audio";
        }

        return "other";
    }

    /**
     * 判断文件是否可以在小程序中预览
     * @param fileType 文件扩展名
     * @return 是否可预览
     */
    private boolean isPreviewable(String fileType) {
        if (StringUtils.isEmpty(fileType)) {
            return false;
        }

        String previewType = determinePreviewType(fileType);

        // 小程序支持预览的类型
        return "image".equals(previewType) || "document".equals(previewType);
    }

    /**
     * 对重定向URL进行编码处理，避免中文字符导致的HTTP头错误
     */
    private String encodeRedirectUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        try {
            // 若存在非ASCII字符
            if (!StandardCharsets.US_ASCII.newEncoder().canEncode(url)) {
                int schemeIdx = url.indexOf("://");
                if (schemeIdx > 0) {
                    int pathIdx = url.indexOf('/', schemeIdx + 3);
                    if (pathIdx > 0) {
                        String base = url.substring(0, pathIdx);
                        String encodedPath = Arrays.stream(url.substring(pathIdx).split("/"))
                                .filter(part -> !part.isEmpty())
                                .map(ServletUtils::urlEncode)
                                .collect(Collectors.joining("/", "/", ""));
                        return base + encodedPath;
                    }
                }
                // 非完整URL或不含路径部分
                return ServletUtils.urlEncode(url);
            }
            return url;
        } catch (Exception e) {
            logger.warn("URL编码失败，使用原始URL: {} - {}", url, e.getMessage());
            return url;
        }
    }

    /**
     * 文件访问结果内部类
     */
    private static class FileAccessResult {
        public Record fileInfo;
        public String accessUrl;
        public String actualStorageType;

        public FileAccessResult(Record fileInfo, String accessUrl, String actualStorageType) {
            this.fileInfo = fileInfo;
            this.accessUrl = accessUrl;
            this.actualStorageType = actualStorageType;
        }
    }

    /**
     * 获取文件信息和访问URL的共同方法
     * @param fileId 文件ID
     * @return 文件访问结果，如果文件不存在返回null
     */
    private FileAccessResult getFileInfoAndUrl(String fileId) {
        try {
            // 查询文件信息
            Record fileInfo = Db.findFirst(
                "SELECT file_id, original_name, access_url, oss_url, oss_key, storage_type, file_type, mime_type, file_size, absolute_path " +
                "FROM eh_file_info WHERE file_id = ? AND status = '0'", fileId);

            if (fileInfo == null) {
                return null;
            }

            String accessUrl = null;
            String storageType = fileInfo.getStr("storage_type");
            String ossKey = fileInfo.getStr("oss_key");
            String localUrl = fileInfo.getStr("access_url");
            String ossUrl = fileInfo.getStr("oss_url");
            String actualStorageType = storageType;

            logger.debug("文件信息: fileId={}, storageType={}, ossKey={}, localUrl={}",
                        fileId, storageType, StringUtils.isNotEmpty(ossKey) ? "有" : "无", localUrl);

            // 根据存储类型生成访问URL，固定1小时过期时间
            if ("both".equals(storageType) || "oss".equals(storageType)) {
                // 优先使用OSS
                if (StringUtils.isNotEmpty(ossKey) && ossService.isAvailable()) {
                    try {
                        long ossStartTime = System.currentTimeMillis();
                        accessUrl = ossService.generatePresignedUrl(ossUrl, ossKey, 3600L); // 固定1小时过期
                        long ossTime = System.currentTimeMillis() - ossStartTime;

                        if (StringUtils.isNotEmpty(accessUrl)) {
                            logger.info("OSS预签名URL生成成功: fileId={}, 耗时={}ms", fileId, ossTime);
                            actualStorageType = "oss";
                        } else {
                            // OSS生成失败，降级到本地文件
                            accessUrl = ensureFullUrl(localUrl);
                            actualStorageType = "local_fallback";
                            logger.warn("OSS预签名URL生成失败，降级使用本地文件: fileId={}", fileId);
                        }
                    } catch (Exception ossException) {
                        // OSS异常，降级到本地文件
                        accessUrl = ensureFullUrl(localUrl);
                        actualStorageType = "local_fallback";
                        logger.warn("OSS访问异常，降级使用本地文件: fileId={}, error={}", fileId, ossException.getMessage());
                    }
                } else {
                    // OSS Key为空或OSS不可用，使用本地文件
                    accessUrl = ensureFullUrl(localUrl);
                    actualStorageType = "local";
                    logger.debug("OSS不可用或无OSS Key，使用本地文件: fileId={}", fileId);
                }
            } else {
                // 本地存储
                accessUrl = ensureFullUrl(localUrl);
                actualStorageType = "local";
                logger.debug("本地存储文件: fileId={}", fileId);
            }

            if (StringUtils.isEmpty(accessUrl)) {
                logger.error("无法生成文件访问URL: fileId={}", fileId);
                return null;
            }

            return new FileAccessResult(fileInfo, accessUrl, actualStorageType);

        } catch (Exception e) {
            logger.error("获取文件信息和URL失败: fileId={}, error={}", fileId, e.getMessage(), e);
            return null;
        }
    }

}
