<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('编辑业主')" />
    <style>
        .binding-info-display {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }

        .binding-text-item {
            margin-bottom: 10px;
            padding: 8px 0;
            display: flex;
            align-items: center;
        }

        .binding-text-item:last-child {
            margin-bottom: 0;
        }

        .binding-label {
            font-weight: 600;
            color: #333;
            min-width: 80px;
            display: inline-block;
        }

        .binding-content {
            flex: 1;
            color: #666;
            margin-right: 10px;
        }

        .binding-manage-link {
            color: #007bff;
            text-decoration: none;
            font-size: 12px;
            padding: 2px 8px;
            border: 1px solid #007bff;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .binding-manage-link:hover {
            background-color: #007bff;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-owner-edit">
            <input name="owner_id" th:value="${owner.owner_id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-2 control-label is-required">姓名：</label>
                <div class="col-sm-4">
                    <input id="owner_name" name="owner_name" class="form-control" type="text" required>
                </div>
                <label class="col-sm-2 control-label is-required">手机号：</label>
                <div class="col-sm-4">
                    <input id="mobile" name="mobile" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-2 control-label">性别：</label>
                <div class="col-sm-4">
                    <select name="gender" class="form-control">
                        <option value="">请选择</option>
                        <option value="M">男</option>
                        <option value="F">女</option>
                    </select>
                </div>
                <label class="col-sm-2 control-label">身份证号码：</label>
                <div class="col-sm-4">
                    <input id="id_card" name="id_card" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">住户状态：</label>
                <div class="col-sm-4">
                    <select name="is_live" class="form-control">
                        <option value="0">未入住</option>
                        <option value="1" >已入住</option>
                        <option value="2" >已迁出</option>
                    </select>
                </div>
                <label class="col-sm-2 control-label">入住日期：</label>
                <div class="col-sm-4">
                    <input name="move_date" type="text" class="form-control time-input">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea id="remark" name="remark" style="height: 70px;" class="form-control"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">绑定信息：</label>
                <div class="col-sm-10">
                    <!-- 绑定信息展示区域 -->
                    <div id="bindingInfoDisplay" class="binding-info-display">
                        <div id="houseBindingInfo" class="binding-text-item">
                            <span class="binding-label">绑定房屋：</span>
                            <span class="binding-content">暂无绑定房屋</span>
                            <a href="javascript:void(0);" onclick="manageBindings();" class="binding-manage-link">去修改</a>
                        </div>
                        <div id="vehicleBindingInfo" class="binding-text-item">
                            <span class="binding-label">绑定车辆：</span>
                            <span class="binding-content">暂无绑定车辆</span>
                            <a href="javascript:void(0);" onclick="manageBindings();" class="binding-manage-link">去修改</a>
                        </div>
                        <div id="parkingBindingInfo" class="binding-text-item">
                            <span class="binding-label">绑定车位：</span>
                            <span class="binding-content">暂无绑定车位</span>
                            <a href="javascript:void(0);" onclick="manageBindings();" class="binding-manage-link">去修改</a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />



    <script type="text/javascript">
        var prefix = ctx + "oc/owner";
        
        $("#form-owner-edit").validate({
            focusCleanup: true
        });
        
        $(function(){
            $('#form-owner-edit').renderForm({url:prefix+'/record'},function(res){

            });
            // 加载绑定信息
            loadBindingInfo();
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-owner-edit').serialize());
            }
        }

        function manageBindings() {
            var ownerId = $("input[name='owner_id']").val();
            if (!ownerId) {
                $.modal.alertWarning("请先保存业主信息");
                return;
            }
            $.modal.popupRight({title:"管理绑定关系", url:prefix + "/houseBindings/" + ownerId, end:function() {
                // 弹窗关闭后刷新绑定信息
                loadBindingInfo();
            }});
        }

        // 加载绑定信息
        function loadBindingInfo() {
            var ownerId = $("input[name='owner_id']").val();
            if (!ownerId) {
                return;
            }

            // 加载房屋绑定信息
            loadHouseBindingInfo(ownerId);
            // 加载车辆绑定信息
            loadVehicleBindingInfo(ownerId);
            // 加载车位绑定信息
            loadParkingBindingInfo(ownerId);
        }

        // 加载房屋绑定信息
        function loadHouseBindingInfo(ownerId) {
            $.ajax({
                url: prefix + "/houseBindings",
                type: "GET",
                data: { ownerId: ownerId },
                success: function(res) {
                    if (res.code === 0) {
                        renderHouseBindingInfo(res.data);
                    }
                },
                error: function() {
                    console.error("加载房屋绑定信息失败");
                }
            });
        }

        // 加载车辆绑定信息
        function loadVehicleBindingInfo(ownerId) {
            $.ajax({
                url: prefix + "/vehicleBindings",
                type: "GET",
                data: { ownerId: ownerId },
                success: function(res) {
                    if (res.code === 0) {
                        renderVehicleBindingInfo(res.data);
                    }
                },
                error: function() {
                    console.error("加载车辆绑定信息失败");
                }
            });
        }

        // 加载车位绑定信息
        function loadParkingBindingInfo(ownerId) {
            $.ajax({
                url: prefix + "/parkingBindings",
                type: "GET",
                data: { ownerId: ownerId },
                success: function(res) {
                    if (res.code === 0) {
                        renderParkingBindingInfo(res.data);
                    }
                },
                error: function() {
                    console.error("加载车位绑定信息失败");
                }
            });
        }

        // 渲染房屋绑定信息
        function renderHouseBindingInfo(data) {
            var contentText = '';
            if (data && data.length > 0) {
                var displayItems = [];
                for (var i = 0; i < data.length; i++) {
                    var house = data[i];
                    var houseText = house.combina_name || '房屋';
                    if (house.room) {
                        houseText += house.room;
                    }
                    if (house.use_area) {
                        houseText += '(' + house.use_area + '㎡)';
                    }
                    displayItems.push(houseText);
                }
                contentText = displayItems.join('、');
            } else {
                contentText = '暂无绑定房屋';
            }
            $('#houseBindingInfo .binding-content').text(contentText);
        }

        // 渲染车辆绑定信息
        function renderVehicleBindingInfo(data) {
            var contentText = '';
            if (data && data.length > 0) {
                var displayItems = [];
                for (var i = 0; i < data.length; i++) {
                    var vehicle = data[i];
                    var vehicleText = vehicle.plate_no;
                    if (vehicle.vehicle_brand || vehicle.vehicle_model) {
                        vehicleText += '(' + (vehicle.vehicle_brand || '') + ' ' + (vehicle.vehicle_model || '') + ')';
                    }
                    displayItems.push(vehicleText);
                }
                contentText = displayItems.join('、');
            } else {
                contentText = '暂无绑定车辆';
            }
            $('#vehicleBindingInfo .binding-content').text(contentText);
        }

        // 渲染车位绑定信息
        function renderParkingBindingInfo(data) {
            var contentText = '';
            if (data && data.length > 0) {
                var displayItems = [];
                for (var i = 0; i < data.length; i++) {
                    var parking = data[i];
                    var parkingText = parking.parking_no;
                    if (parking.parking_type) {
                        var typeText = parking.parking_type == 1 ? '私人车位' :
                                      parking.parking_type == 2 ? '子母车位' : '车位';
                        parkingText += '(' + typeText + ')';
                    }
                    displayItems.push(parkingText);
                }
                contentText = displayItems.join('、');
            } else {
                contentText = '暂无绑定车位';
            }
            $('#parkingBindingInfo .binding-content').text(contentText);
        }
    </script>
</body>
</html>
