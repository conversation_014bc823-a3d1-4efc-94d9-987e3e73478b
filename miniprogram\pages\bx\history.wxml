<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<view class="history-page">
  <view wx:if="{{loading}}" class="loading">加载中...</view>
  <view wx:elif="{{list.length === 0}}" class="empty">暂无反馈记录</view>
  <block wx:else>
    <view class="history-item" wx:for="{{list}}" wx:key="id">
      <!-- 标题行：图标+地址+状态 -->
      <view class="item-header" bindtap="goToDetail" data-id="{{item.id}}">
        <view class="header-left">
          <text class="location-icon">🏠</text>
          <text class="address">{{item.address || '--'}}</text>
        </view>
        <view class="status-tag" style="color: {{item.statusColor}};">
          {{item.statusText}}
        </view>
      </view>

      <!-- 类型徽章 -->
      <view class="type-badge" bindtap="goToDetail" data-id="{{item.id}}">
        <text class="badge-text">{{item.type || '公共报修'}}</text>
      </view>

      <!-- 内容区域 -->
      <view class="content-section" bindtap="goToDetail" data-id="{{item.id}}">
        <text class="content-text">{{item.content}}</text>
      </view>

      <!-- 图片展示区域 -->
      <view wx:if="{{item.imageUrls && item.imageUrls.length}}" class="images-section">
        <view class="images-grid">
          <view
            class="image-item"
            wx:for="{{item.imageUrls}}"
            wx:for-item="imageUrl"
            wx:key="*this"
            wx:if="{{index < 3}}"
            data-url="{{imageUrl}}"
            data-item="{{item}}"
            bindtap="onPreviewImage"
          >
            <image
              class="image"
              src="{{imageUrl}}"
              mode="aspectFill"
              lazy-load
            />
          </view>
        </view>
      </view>

      <!-- 底部操作区 -->
      <view class="item-footer">
        <text class="time">{{item.createTime}}</text>
        <view class="detail-btn" bindtap="goToDetail" data-id="{{item.id}}">
          <text class="detail-text">查看详情</text>
          <van-icon name="arrow" size="24rpx" color="#1890ff" custom-class="arrow-icon" />
        </view>
      </view>
    </view>
  </block>
</view>