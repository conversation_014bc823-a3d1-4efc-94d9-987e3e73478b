.history-page {
  padding: 32rpx;
  background: #f7f8fa;
  min-height: 100vh;
}

.loading, .empty {
  text-align: center;
  color: #aaa;
  margin-top: 80rpx;
}

.history-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 标题行样式 */
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 16rpx 32rpx;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.location-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.address {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.status-tag {
  font-size: 28rpx;
  font-weight: 500;
}

/* 内容区域样式 */
.content-section {
  margin: 0 32rpx 16rpx 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.content-text {
  font-size: 30rpx;
  color: #666;
  line-height: 1.5;
}

/* 图片展示区域 */
.images-section {
  padding: 0 32rpx 16rpx 32rpx;
}

.images-grid {
  display: flex;
  gap: 16rpx;
}

.image-item {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f5f5f5;
}

.image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

/* 附件展示区域 */
.attachments-section {
  padding: 0 32rpx 16rpx 32rpx;
}

.attachments-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 12rpx;
  min-width: 200rpx;
  max-width: 300rpx;
  border: 1rpx solid #e9ecef;
}

.attachment-thumb {
  width: 60rpx;
  height: 60rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.file-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e6f7ff;
  border-radius: 6rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
  font-size: 32rpx;
  color: #1890ff;
}

.attachment-info {
  flex: 1;
  min-width: 0;
}

.attachment-name {
  display: block;
  font-size: 24rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4rpx;
}

.attachment-size {
  display: block;
  font-size: 20rpx;
  color: #999;
}

/* 底部操作区样式 */
.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 32rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.time {
  color: #999;
  font-size: 24rpx;
}

.detail-btn {
  display: flex;
  align-items: center;
  color: #1890ff;
  font-size: 28rpx;
}

.detail-text {
  margin-right: 8rpx;
}

.arrow-icon {
  margin-left: 4rpx;
}